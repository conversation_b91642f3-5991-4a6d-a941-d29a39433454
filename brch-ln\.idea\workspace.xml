<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="09de3a04-62f1-4de7-bfd1-1aa27d325181" name="更改" comment="refactor: 删除合同同步测试类&#10;&#10;删除了 sccl-module-business模块中 ContractSyncTest 类。这个类包含了以下功能：&#10;- FTP 连接和文件列表获取测试&#10;- 合同同步功能测试&#10;由于这些功能可能已经不再使用或已被其他测试替代，因此删除了整个测试类。">
      <change beforePath="$PROJECT_DIR$/sccl-module-business/src/main/java/com/sccl/modules/business/ammeterorprotocol/mapper/AmmeterorprotocolMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/sccl-module-business/src/main/java/com/sccl/modules/business/ammeterorprotocol/mapper/AmmeterorprotocolMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sccl-module-business/src/main/java/com/sccl/modules/mssaccount/mssinterface/controller/MssInterfaceController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sccl-module-business/src/main/java/com/sccl/modules/mssaccount/mssinterface/controller/MssInterfaceController.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="李燕 &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\soft\apache-maven-3.6.0" />
        <option name="localRepository" value="E:\.m2\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\.m2\settings-cl.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zrSLfttJI2apeKYnX8uayELKMM" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.sccl-basic-frame [clean].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.BusinessServiceApplication.executor": "JRebel Executor",
    "Spring Boot.CtscAssessApplication.executor": "JRebel Executor",
    "Spring Boot.CtscDischargeApplication.executor": "JRebel Executor",
    "Spring Boot.CtscLibraryApplication.executor": "JRebel Executor",
    "Spring Boot.web.executor": "JRebel Executor",
    "git-widget-placeholder": "main__20250723",
    "last_opened_file_path": "E:/cl-project/ln-nenghao/brch-ln/carbon-library/library-service/src/main/resources",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "SDK",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true",
    "应用程序.AttachmentsController.executor": "Run",
    "应用程序.MssInterfaceServiceImpl.executor": "Run"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\cl-project\ln-nenghao\brch-ln\carbon-library\library-service\src\main\resources" />
      <recent name="E:\cl-project\ln-nenghao\brch-ln\docker" />
      <recent name="E:\cl-project\ln-nenghao\brch-ln\sccl-module-business\src\main\java\com\sccl\modules\business" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\cl-project\ln-nenghao\brch-ln\docs" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.web">
    <configuration name="AttachmentsController" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.sccl.modules.system.attachments.controller.AttachmentsController" />
      <module name="sccl-module-base" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.sccl.modules.system.attachments.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MssInterfaceServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.sccl.modules.mssaccount.mssinterface.service.MssInterfaceServiceImpl" />
      <module name="sccl-module-business" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.sccl.modules.mssaccount.mssinterface.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BusinessServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="business-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.enrising.ctsc.business.BusinessServiceApplication" />
      <option name="VM_PARAMETERS" value="-Xms512m -Xmx1g -XX:MaxMetaspaceSize=512m -XX:+UseG1GC" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CtscAssessApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="assess-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.enrising.ctsc.assess.CtscAssessApplication" />
      <option name="VM_PARAMETERS" value="-Xms512m -Xmx1g -XX:MaxMetaspaceSize=512m -XX:+UseG1GC" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CtscDischargeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="discharge-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.enrising.ctsc.discharge.CtscDischargeApplication" />
      <option name="VM_PARAMETERS" value="-Xms512m -Xmx1g -XX:MaxMetaspaceSize=512m -XX:+UseG1GC" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CtscLibraryApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="library-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.enrising.ctsc.library.CtscLibraryApplication" />
      <option name="VM_PARAMETERS" value="-Xms512m -Xmx1g -XX:MaxMetaspaceSize=512m -XX:+UseG1GC" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="interface" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sccl-interface" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sccl.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="web" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="ACTIVE_PROFILES" value="dev-ln" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sccl-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sccl.Application" />
      <option name="VM_PARAMETERS" value="-Xms512m -Xmx2g -XX:MaxMetaspaceSize=512m -XX:+UseG1GC" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.BusinessServiceApplication" />
      <item itemvalue="Spring Boot.CtscAssessApplication" />
      <item itemvalue="Spring Boot.CtscDischargeApplication" />
      <item itemvalue="Spring Boot.CtscLibraryApplication" />
      <item itemvalue="Spring Boot.interface" />
      <item itemvalue="Spring Boot.web" />
      <item itemvalue="应用程序.AttachmentsController" />
      <item itemvalue="应用程序.MssInterfaceServiceImpl" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.AttachmentsController" />
        <item itemvalue="应用程序.MssInterfaceServiceImpl" />
        <item itemvalue="应用程序.MssInterfaceServiceImpl" />
        <item itemvalue="应用程序.MssInterfaceServiceImpl" />
        <item itemvalue="应用程序.MssInterfaceServiceImpl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="09de3a04-62f1-4de7-bfd1-1aa27d325181" name="更改" comment="" />
      <created>1752487644949</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752487644949</updated>
      <workItem from="1752487646057" duration="5007000" />
      <workItem from="1752542397556" duration="11476000" />
      <workItem from="1752628769624" duration="13756000" />
      <workItem from="1752716545089" duration="22449000" />
      <workItem from="1752801270756" duration="13323000" />
      <workItem from="1752822576506" duration="5522000" />
      <workItem from="1753059491364" duration="20785000" />
      <workItem from="1753148066574" duration="1214000" />
      <workItem from="1753152259397" duration="24177000" />
      <workItem from="1753234567484" duration="15136000" />
      <workItem from="1753321626614" duration="7776000" />
      <workItem from="1753342020577" duration="3165000" />
      <workItem from="1753405856587" duration="5000" />
      <workItem from="1753424536314" duration="18709000" />
      <workItem from="1753665624517" duration="7894000" />
      <workItem from="1753757380008" duration="6347000" />
      <workItem from="1753839452392" duration="9097000" />
      <workItem from="1753872650392" duration="947000" />
      <workItem from="1753874998309" duration="2293000" />
      <workItem from="1753877342302" duration="217000" />
      <workItem from="1753925259236" duration="17766000" />
      <workItem from="1754009965479" duration="13641000" />
    </task>
    <task id="LOCAL-00021" summary="refactor(database): 优化数据库连接池配置并统一配置格式&#10;&#10;- 移除各服务中不必要的注释，统一配置格式&#10;- 添加 initialization-fail-timeout 和 register-mbeans 配置项&#10;- 修改部分服务的 pool-name 配置&#10;- 优化 carbon-common 中的 JwtUtils 类，完善用户信息获取逻辑">
      <option name="closed" value="true" />
      <created>1753059711113</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753059711113</updated>
    </task>
    <task id="LOCAL-00022" summary="fix(wftask): 修复用户公司为空时的任务查询异常&#10;&#10;- 在设置 busiDeptCode 之前增加对 user.getCompanies() 的非空检查">
      <option name="closed" value="true" />
      <created>1753063217222</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753063217222</updated>
    </task>
    <task id="LOCAL-00023" summary="feat(mssinterface): 新增按期号和报账单编码">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00024" summary="feat(mssinterface): 优化按期号和报账单编码">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00025" summary="fix(mssaccount): 添加电表信息导入时的供电方式检查">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00026" summary="fix(mssaccount): 添加电表信息导入时的供电方式检查">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00027" summary="refactor: 更新流程引擎 PI地址&#10;&#10;- 将本地开发环境中的 PI 地址从127.0.0.1 更改为 10.206.23.226&#10;- 此修改使开发环境配置与实际运行环境保持一致">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00028" summary="feat(mssaccount): 修复超出报账的电量功能">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00029" summary="feat(mssaccount): 修复超出报账的电量功能">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00030" summary="feat(mssaccount): 修复超出报账的电量功能">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00031" summary="fix(sccl-module-business): 修正 getCollectMeterByCountyAndWriteoff 查询结果&#10;&#10;- 调整查询结果的分组方式，增加 cityCode、cityName、countyCode、countyName、stationName、county_name 到 GROUP BY 子句&#10;- 修改 this_quantity_of_electricity 和 mss_data 的计算方式，使用 SUM 函数进行汇总- 优化查询结构，简化子查询的嵌套层次&#10;-调整 SELECT 语句的格式，使用 AS 关键字明确列别名">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00032" summary="feat(mssaccount): 增加 90 天外数据处理支持&#10;&#10;- 新增 processDateRangeWithEnergyDistributionEnhanced 方法，用于处理 90 天外的数据&#10;- 如果所有数据都在90 天外且 90 天内无有效数据，生成最近 90 天的数据进行分摊&#10;- 优化数据合并和能耗分配逻辑，确保准确处理跨月和跨年数据- 增加日志记录和异常处理，提高系统稳定性和可维护性">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00033" summary="feat(business): 合并相同局站的采集数据&#10;&#10;- 在 MssInterfaceServiceImpl 中添加数据合并逻辑&#10;- 修改 SQL 查询以按 stationCode 和 energy_meter_code 分组&#10;- 实现 mergeQueryResultsByStationCode 和 mergeStationResults 方法- 更新日志信息和结果消息以反映数据合并过程">
      <option name="closed" value="true" />
      <created>1753184473349</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753184473349</updated>
    </task>
    <task id="LOCAL-00034" summary="feat(sccl-module-business): 支持负数能耗的均摊和电量调整&#10;&#10;- 在 calculateEnergyDistribution 和 calculateEnergyDistributionForRecentDays 方法中增加对负数能耗的支持- 修改 adjustElectricityInValidRange 方法，支持正数（减少）和负数（增加）的电量调整&#10;- 增加日志记录，以便追踪负数均摊和调整的过程&#10;- 优化电量调整逻辑，确保调整后的电量可以为负数">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00035" summary="refactor(mssaccount): 优化报账数据时间区间处理逻辑- 移除了生成最近90天数据的逻辑，只在实际存在的时间区间内进行数据分摊&#10;- 新增 analyzeActualDateRange 方法，用于分析实际日期范围- 新增 calculateEnergyDistributionForActualRange 方法，用于实际时间区间的能耗分配&#10;- 优化了数据处理流程，提高了代码的可读性和维护性">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00036" summary="fix(business): 修正电表电量查询的起始日期&#10;&#10;- 将 min(start_date) 修改为 max(start_date)，以确保获取正确的起始日期&#10;- 这个修改解决了电量数据不准确的问题">
      <option name="closed" value="true" />
      <created>1753237640481</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753237640481</updated>
    </task>
    <task id="LOCAL-00037" summary="refactor(sccl-module-business):优化能耗数据日均值计算逻辑&#10;&#10;- 将原始数据视为总电量数据，而非日均值- 计算日均值时，将总电量数据除以有效天数&#10;- 移除了对排除日期的特殊处理逻辑&#10;- 优化了日志输出，更准确地反映了数据分摊过程">
      <option name="closed" value="true" />
      <created>1753238328893</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753238328893</updated>
    </task>
    <task id="LOCAL-00038" summary="refactor(sccl-module-business):优化日志记录细节&#10;&#10;- 在合并记录时添加详细的合并日志，包括局站名称、合并记录数、日期范围和电量变化- 在分摊电量时，将日志内容扩展为包含时间区间、总电量、有效天数和日均电量">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00039" summary="refactor(mssaccount): 优化电量调整策略，支持增减电量&#10;&#10;- 重构电量调整逻辑，支持正数减少和负数增加电量&#10;- 优化调整算法，实现更精确的电量调节&#10;- 完善日志记录，提高代码可读性和可维护性">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00040" summary="成本数字化：业财一致率、局站业务电量查询，从四川迁移到辽宁">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00041" summary="1、局站业务电量查询：定时器调整。&#10;2、增加定时任务：基站一致率异常稽核">
      <option name="closed" value="true" />
      <created>1753249120806</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1753249120806</updated>
    </task>
    <task id="LOCAL-00042" summary="refactor(sccl-module-business):移除未使用的接口方法&#10;&#10;- 删除了 IMssInterfaceService 接口中未使用的 sendMeterInfo 方法&#10;- 简化了接口定义，提高了代码的可维护性">
      <option name="closed" value="true" />
      <created>1753249346299</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1753249346299</updated>
    </task>
    <task id="LOCAL-00043" summary="refactor(business): 移除无用的代码行&#10;&#10;- 删除了 MeterinfoAllJtApplyController 类中的 sendMeterInfo 方法调用">
      <option name="closed" value="true" />
      <created>1753249478686</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1753249478686</updated>
    </task>
    <task id="LOCAL-00044" summary="fix(sccl-web): 修改流程引擎 PI 地址端口&#10;&#10;- 将流程引擎 PI 地址的端口从 8080 修改为 9080&#10;- 更新配置文件以确保服务正常连接到新的 PI 地址">
      <option name="closed" value="true" />
      <created>1753328060245</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1753328060245</updated>
    </task>
    <task id="LOCAL-00045" summary="build(docker): 调整 Docker 配置文件中的内存分配&#10;&#10;- 将 assess、business、discharge 和 library 服务的初始内存分配从 128M 增加到 512M&#10;- 将 assess、business、discharge 和 library 服务的最大内存分配从 2G减少到 1G&#10;- 将 web 服务的最大内存分配从 8G 减少到 4G">
      <option name="closed" value="true" />
      <created>1753335761251</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1753335761251</updated>
    </task>
    <task id="LOCAL-00046" summary="refactor(sccl-module-business):优化代码格式和结构&#10;&#10;-调整代码缩进和空格&#10;- 修复部分代码注释&#10;- 优化部分变量命名&#10;- 删除冗余的空行">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00047" summary="feat(mssaccount): 添加同步智能电表数据功能&#10;&#10;- 新增同步智能电表数据的接口和实现&#10;- 增加数据查询、修正和推送的逻辑- 支持单个日期和批量日期的同步&#10;- 添加日志记录和异常处理">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00048" summary="fix(business): 修复查询电表数据时 countyCode 为空的问题&#10;&#10;- 在查询语句中添加了 WHERE countyCode is not null条件&#10;- 确保返回的电表数据中 countyCode 字段不为空">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00049" summary="fix(sccl-module-business): 清除推送数据中的数据库字段&#10;&#10;- 在推送数据前，清除 CollectMeter 对象中的数据库相关字段&#10;- 主要包括主键 ID、删除标志、同步标志和失败信息等字段&#10;- 这些字段不满足集团接口协议要求，避免因字段问题导致接口失败">
      <option name="closed" value="true" />
      <created>1753438512049</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1753438512049</updated>
    </task>
    <task id="LOCAL-00050" summary="fix(sccl-module-business): 修复电表数据采集查询条件&#10;&#10;- 在 AmmeterorprotocolMapper.xml 文件中的查询语句中添加了 eneryType 的过滤条件&#10;-仅选取 eneryType 为1 或 6 的记录，以确保数据准确性">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00051" summary="fix(mssaccount): 避免推送时包含 ID">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00052" summary="refactor(mssaccount): 重构集采数据同步接口&#10;&#10;-移除了 CollectMeter 类中的冗余字段：id、del_flag、syncFlag、failMag&#10;- 更新了 CollectMeterFail 类，将相关字段改为字符串类型&#10;- 调整了数据处理逻辑，不再手动设置和清除特定字段&#10;-优化了与集团接口的数据交互方式">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00053" summary="fix(business): 修复总表电量为负数时的数据异常- 在 AmmeterorprotocolMapper.xml 文件中，为查询语句添加了 t.totalMeterEnergy &gt;= 0 的条件&#10;- 此修改确保了只选取总表电量大于等于 0 的记录，避免了负数电量导致的数据异常">
      <option name="closed" value="true" />
      <created>1753447638541</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1753447638541</updated>
    </task>
    <task id="LOCAL-00054" summary="perf(http): 优化 HTTP 连接设置&#10;&#10;- 将连接超时时间从 10 秒调整为 5 秒&#10;- 取消 HTTP 请求的重试机制">
      <option name="closed" value="true" />
      <created>1753767283526</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1753767283526</updated>
    </task>
    <task id="LOCAL-00055" summary="feat(wftask): 增加 OA 推送配置控制- 在 WfTaskServiceImpl 中添加 ConfigMapper 依赖&#10;- 在 sendTask2OA 方法中增加推送配置检查&#10;- 如果未开启 OA推送，记录日志并返回">
      <option name="closed" value="true" />
      <created>1753778635220</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1753778635220</updated>
    </task>
    <task id="LOCAL-00056" summary="feat(minio): 优化预览 URL 获取接口&#10;&#10;- 修改 getPreviewUrl 接口，增加 bucketName 参数&#10;- 新增 getViewUrl 方法，支持指定 bucketName 和 URL- 优化代码结构，提高可维护性和可扩展性">
      <option name="closed" value="true" />
      <created>1753841345348</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1753841345348</updated>
    </task>
    <task id="LOCAL-00057" summary="fix(attachments): 替换附件 URL 中的内部 IP 为外部 IP&#10;&#10;- 在 AttachmentsController 中，查询附件列表后，遍历每个附件对象&#10;- 使用正则表达式将 URL 中的内部 IP 地址 ***********替换为外部 IP 地址 **************- 这个修改解决了附件 URL 内部 IP 外网不可访问的问题，确保了附件可以在外部网络正常访问">
      <option name="closed" value="true" />
      <created>1753842314723</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1753842314723</updated>
    </task>
    <task id="LOCAL-00058" summary="fix(attachments): 替换附件 URL 中的内部 IP 为外部 IP&#10;&#10;- 在 AttachmentsController 中，查询附件列表后，遍历每个附件对象&#10;- 使用正则表达式将 URL 中的内部 IP 地址 ***********替换为外部 IP 地址 **************- 这个修改解决了附件 URL 内部 IP 外网不可访问的问题，确保了附件可以在外部网络正常访问">
      <option name="closed" value="true" />
      <created>1753842488741</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1753842488741</updated>
    </task>
    <task id="LOCAL-00059" summary="feat(minio): 替换 URL 中的 IP 为外部 IP- 在 MinioUtil 类中的 getViewUrl 方法中，将内部 IP &quot;***********&quot; 替换为外部 IP &quot;**************&quot;&#10;- 添加日志记录替换后的 URL">
      <option name="closed" value="true" />
      <created>1753843368530</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1753843368530</updated>
    </task>
    <task id="LOCAL-00060" summary="fix(minio): 修正预签名URL的IP地址替换逻辑&#10;&#10;- 移除了初始URL的IP地址替换逻辑&#10;- 在生成预签名URL后，替换了其中的IP地址&#10;- 更新了日志信息，以反映新的替换逻辑">
      <option name="closed" value="true" />
      <created>1753852571305</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1753852571305</updated>
    </task>
    <task id="LOCAL-00061" summary="refactor(minio): 优化生成临时授权URL的代码逻辑&#10;&#10;- 移除了硬编码的IP地址替换逻辑&#10;- 采用更灵活的字符串处理方式，仅保留所需的路径部分&#10;- 简化了代码并提高了可维护性">
      <option name="closed" value="true" />
      <created>1753854272273</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1753854272274</updated>
    </task>
    <task id="LOCAL-00062" summary="refactor(minio): 优化获取预览 URL 的逻辑&#10;&#10;-将 MinioController 中的 getPreviewUrl 方法参数从 url 改为 objectName，提高代码可读性&#10;- 重构 MinioUtil 中的 getViewUrl 方法，移除不必要的字符串处理&#10;- 优化预览 URL 的生成逻辑，直接使用 bucketName 进行拼接">
      <option name="closed" value="true" />
      <created>1753858762831</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1753858762831</updated>
    </task>
    <task id="LOCAL-00063" summary="refactor(minio): 优化获取预览 URL 的逻辑">
      <option name="closed" value="true" />
      <created>1753875031371</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1753875031371</updated>
    </task>
    <task id="LOCAL-00064" summary="refactor(business): 修复业财一致率报表界面报错且无数据">
      <option name="closed" value="true" />
      <created>1753929852633</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1753929852633</updated>
    </task>
    <task id="LOCAL-00065" summary="feat(budgetsetting): 新增获取全省预算汇总信息功能&#10;&#10;- 在 BudgetSettingMapper 中添加 getBudgetSettingProvince 方法&#10;- 在 BudgetSettingMapper.xml 中实现 getBudgetSettingProvince 方法的 SQL 查询&#10;- 修改 BudgetSettingServiceImpl 中的 getBudgetSetting 方法，使用新的 getBudgetSettingProvince 方法获取数据&#10;- 修正上一年份的计算逻辑，改为 year -1">
      <option name="closed" value="true" />
      <created>1753934115270</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1753934115270</updated>
    </task>
    <task id="LOCAL-00066" summary="style(sccl-module-business): 优化 事后稽核 SQL 查询语句">
      <option name="closed" value="true" />
      <created>1753947678078</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1753947678078</updated>
    </task>
    <task id="LOCAL-00067" summary="feat(business): 添加合同同步功能&#10;&#10;- 实现了合同文件的FTP同步功能，包括文件列表获取和文件下载&#10;- 新增合同同步控制器、服务接口、服务实现类和相关配置类&#10;- 添加了合同同步结果实体类和相关测试类&#10;- 更新了pom文件，将hutool-all版本升级到5.8.37">
      <option name="closed" value="true" />
      <created>1754013896845</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1754013896845</updated>
    </task>
    <task id="LOCAL-00068" summary="build(sccl-module-business): 添加 Apache Commons Net 依赖&#10;&#10;- 在 sccl-module-business 模块的 pom.xml 文件中添加了 Apache Commons Net 依赖&#10;- 版本号为 3.9.0- 此依赖用于支持 hutool FTP 功能">
      <option name="closed" value="true" />
      <created>1754015917720</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1754015917720</updated>
    </task>
    <task id="LOCAL-00069" summary="refactor: 删除合同同步测试类&#10;&#10;删除了 sccl-module-business模块中 ContractSyncTest 类。这个类包含了以下功能：&#10;- FTP 连接和文件列表获取测试&#10;- 合同同步功能测试&#10;由于这些功能可能已经不再使用或已被其他测试替代，因此删除了整个测试类。">
      <option name="closed" value="true" />
      <created>1754041455598</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1754041455598</updated>
    </task>
    <option name="localTasksCounter" value="70" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/main_20250710" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="李燕" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="fix(sccl-web): 修改流程引擎 PI 地址端口&#10;&#10;- 将流程引擎 PI 地址的端口从 8080 修改为 9080&#10;- 更新配置文件以确保服务" />
    <MESSAGE value="build(docker): 调整 Docker 配置文件中的内存分配&#10;&#10;- 将 assess、business、discharge 和 library 服务的初始内存分配从 128M 增加到 512M&#10;- 将 assess、business、discharge 和 library 服务的最大内存分配从 2G减少到 1G&#10;- 将 web 服务的最大内存分配从 8G 减少到 4G" />
    <MESSAGE value="refactor(sccl-module-business):优化代码格式和结构&#10;&#10;-调整代码缩进和空格&#10;- 修复部分代码注释&#10;- 优化部分变量命名&#10;- 删除冗余的空行" />
    <MESSAGE value="feat(mssaccount): 添加同步智能电表数据功能&#10;&#10;- 新增同步智能电表数据的接口和实现&#10;- 增加数据查询、修正和推送的逻辑- 支持单个日期和批量日期的同步&#10;- 添加日志记录和异常处理" />
    <MESSAGE value="fix(business): 修复查询电表数据时 countyCode 为空的问题&#10;&#10;- 在查询语句中添加了 WHERE countyCode is not null条件&#10;- 确保返回的电表数据中 countyCode 字段不为空" />
    <MESSAGE value="fix(sccl-module-business): 清除推送数据中的数据库字段&#10;&#10;- 在推送数据前，清除 CollectMeter 对象中的数据库相关字段&#10;- 主要包括主键 ID、删除标志、同步标志和失败信息等字段&#10;- 这些字段不满足集团接口协议要求，避免因字段问题导致接口失败" />
    <MESSAGE value="fix(sccl-module-business): 修复电表数据采集查询条件&#10;&#10;- 在 AmmeterorprotocolMapper.xml 文件中的查询语句中添加了 eneryType 的过滤条件&#10;-仅选取 eneryType 为1 或 6 的记录，以确保数据准确性" />
    <MESSAGE value="fix(mssaccount): 避免推送时包含 ID" />
    <MESSAGE value="refactor(mssaccount): 重构集采数据同步接口&#10;&#10;-移除了 CollectMeter 类中的冗余字段：id、del_flag、syncFlag、failMag&#10;- 更新了 CollectMeterFail 类，将相关字段改为字符串类型&#10;- 调整了数据处理逻辑，不再手动设置和清除特定字段&#10;-优化了与集团接口的数据交互方式" />
    <MESSAGE value="fix(business): 修复总表电量为负数时的数据异常" />
    <MESSAGE value="perf(http): 优化 HTTP 连接设置&#10;&#10;- 将连接超时时间从 10 秒调整为 5 秒&#10;- 取消 HTTP 请求的重试机制" />
    <MESSAGE value="feat(wftask): 增加 OA 推送配置控制- 在 WfTaskServiceImpl 中添加 ConfigMapper 依赖&#10;- 在 sendTask2OA 方法中增加推送配置检查&#10;- 如果未开启 OA推送，记录日志并返回" />
    <MESSAGE value="feat(minio): 优化预览 URL 获取接口&#10;&#10;- 修改 getPreviewUrl 接口，增加 bucketName 参数&#10;- 新增 getViewUrl 方法，支持指定 bucketName 和 URL- 优化代码结构，提高可维护性和可扩展性" />
    <MESSAGE value="fix(attachments): 替换附件 URL 中的内部 IP 为外部 IP&#10;&#10;- 在 AttachmentsController 中，查询附件列表后，遍历每个附件对象&#10;- 使用正则表达式将 URL 中的内部 IP 地址 ***********替换为外部 IP 地址 **************- 这个修改解决了附件 URL 内部 IP 外网不可访问的问题，确保了附件可以在外部网络正常访问" />
    <MESSAGE value="feat(minio): 替换 URL 中的 IP 为外部 IP- 在 MinioUtil 类中的 getViewUrl 方法中，将内部 IP &quot;***********&quot; 替换为外部 IP &quot;**************&quot;&#10;- 添加日志记录替换后的 URL" />
    <MESSAGE value="fix(minio): 修正预签名URL的IP地址替换逻辑&#10;&#10;- 移除了初始URL的IP地址替换逻辑&#10;- 在生成预签名URL后，替换了其中的IP地址&#10;- 更新了日志信息，以反映新的替换逻辑" />
    <MESSAGE value="refactor(minio): 优化生成临时授权URL的代码逻辑&#10;&#10;- 移除了硬编码的IP地址替换逻辑&#10;- 采用更灵活的字符串处理方式，仅保留所需的路径部分&#10;- 简化了代码并提高了可维护性" />
    <MESSAGE value="refactor(minio): 优化获取预览 URL 的逻辑&#10;&#10;-将 MinioController 中的 getPreviewUrl 方法参数从 url 改为 objectName，提高代码可读性&#10;- 重构 MinioUtil 中的 getViewUrl 方法，移除不必要的字符串处理&#10;- 优化预览 URL 的生成逻辑，直接使用 bucketName 进行拼接" />
    <MESSAGE value="refactor(minio): 优化获取预览 URL 的逻辑" />
    <MESSAGE value="refactor(business): 修复业财一致率报表界面报错且无数据" />
    <MESSAGE value="feat(budgetsetting): 新增获取全省预算汇总信息功能&#10;&#10;- 在 BudgetSettingMapper 中添加 getBudgetSettingProvince 方法&#10;- 在 BudgetSettingMapper.xml 中实现 getBudgetSettingProvince 方法的 SQL 查询&#10;- 修改 BudgetSettingServiceImpl 中的 getBudgetSetting 方法，使用新的 getBudgetSettingProvince 方法获取数据&#10;- 修正上一年份的计算逻辑，改为 year -1" />
    <MESSAGE value="style(sccl-module-business): 优化 事后稽核 SQL 查询语句" />
    <MESSAGE value="feat(business): 添加合同同步功能&#10;&#10;- 实现了合同文件的FTP同步功能，包括文件列表获取和文件下载&#10;- 新增合同同步控制器、服务接口、服务实现类和相关配置类&#10;- 添加了合同同步结果实体类和相关测试类&#10;- 更新了pom文件，将hutool-all版本升级到5.8.37" />
    <MESSAGE value="build(sccl-module-business): 添加 Apache Commons Net 依赖&#10;&#10;- 在 sccl-module-business 模块的 pom.xml 文件中添加了 Apache Commons Net 依赖&#10;- 版本号为 3.9.0- 此依赖用于支持 hutool FTP 功能" />
    <MESSAGE value="refactor: 删除合同同步测试类&#10;&#10;删除了 sccl-module-business模块中 ContractSyncTest 类。这个类包含了以下功能：&#10;- FTP 连接和文件列表获取测试&#10;- 合同同步功能测试&#10;由于这些功能可能已经不再使用或已被其他测试替代，因此删除了整个测试类。" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor: 删除合同同步测试类&#10;&#10;删除了 sccl-module-business模块中 ContractSyncTest 类。这个类包含了以下功能：&#10;- FTP 连接和文件列表获取测试&#10;- 合同同步功能测试&#10;由于这些功能可能已经不再使用或已被其他测试替代，因此删除了整个测试类。" />
  </component>
</project>